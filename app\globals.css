@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens to match design brief with emerald primary and modern palette */
  --background: #ffffff;
  --foreground: #475569;
  --card: #f1f5f9;
  --card-foreground: #374151;
  --popover: #ffffff;
  --popover-foreground: #475569;
  --primary: #059669;
  --primary-foreground: #ffffff;
  --secondary: #10b981;
  --secondary-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #374151;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #f1f5f9;
  --ring: rgba(5, 150, 105, 0.5);
  --chart-1: #059669;
  --chart-2: #10b981;
  --chart-3: #ea580c;
  --chart-4: #dc2626;
  --chart-5: #f59e0b;
  --radius: 0.5rem;
  --sidebar: #f1f5f9;
  --sidebar-foreground: #374151;
  --sidebar-primary: #059669;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #10b981;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: rgba(5, 150, 105, 0.5);
}

.dark {
  /* Updated dark mode tokens to complement the new emerald theme */
  --background: #0f172a;
  --foreground: #e2e8f0;
  --card: #1e293b;
  --card-foreground: #e2e8f0;
  --popover: #1e293b;
  --popover-foreground: #e2e8f0;
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: #059669;
  --secondary-foreground: #ffffff;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: rgba(16, 185, 129, 0.5);
  --chart-1: #10b981;
  --chart-2: #059669;
  --chart-3: #f97316;
  --chart-4: #ef4444;
  --chart-5: #eab308;
  --sidebar: #1e293b;
  --sidebar-foreground: #e2e8f0;
  --sidebar-primary: #10b981;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #059669;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #334155;
  --sidebar-ring: rgba(16, 185, 129, 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added custom animations and micro-interactions */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(5, 150, 105, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(5, 150, 105, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: linear-gradient(135deg, #059669, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
